/**
 * 编辑器容器样式
 * 
 * 定义了DL-Engine编辑器的基础样式和主题
 */

/* 编辑器容器基础样式 */
.editor-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #1e1e1e;
  color: #ffffff;
  overflow: hidden;
}

/* 停靠面板样式覆盖 */
.dock-layout {
  background-color: #1e1e1e;
}

.dock-panel {
  background-color: #252526;
  border: 1px solid #3c3c3c;
}

.dock-tab {
  background-color: #2d2d30;
  border: 1px solid #3c3c3c;
  color: #cccccc;
  padding: 8px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dock-tab:hover {
  background-color: #37373d;
  color: #ffffff;
}

.dock-tab.dock-tab-active {
  background-color: #1e1e1e;
  color: #ffffff;
  border-bottom-color: #007acc;
}

.dock-tab-close-btn {
  color: #cccccc;
  margin-left: 8px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.dock-tab-close-btn:hover {
  opacity: 1;
  color: #ffffff;
}

.dock-content {
  background-color: #1e1e1e;
  padding: 0;
  overflow: hidden;
}

/* 分割器样式 */
.dock-divider {
  background-color: #3c3c3c;
}

.dock-divider:hover {
  background-color: #007acc;
}

/* 场景编辑器样式 */
.scene-editor {
  background-color: #2a2a2a;
  position: relative;
}

.scene-editor canvas {
  display: block;
  outline: none;
}

/* 工具栏样式 */
.editor-toolbar {
  background-color: #2d2d30;
  border-bottom: 1px solid #3c3c3c;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 40px;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 8px;
  border-right: 1px solid #3c3c3c;
}

.toolbar-group:last-child {
  border-right: none;
}

.toolbar-button {
  background-color: transparent;
  border: 1px solid transparent;
  color: #cccccc;
  padding: 6px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-button:hover {
  background-color: #37373d;
  border-color: #3c3c3c;
  color: #ffffff;
}

.toolbar-button.active {
  background-color: #007acc;
  border-color: #007acc;
  color: #ffffff;
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 面板标题样式 */
.panel-header {
  background-color: #2d2d30;
  border-bottom: 1px solid #3c3c3c;
  padding: 8px 12px;
  font-size: 13px;
  font-weight: 600;
  color: #cccccc;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-content {
  background-color: #1e1e1e;
  padding: 12px;
  overflow-y: auto;
  height: calc(100% - 40px);
}

/* 层次结构面板样式 */
.hierarchy-panel {
  background-color: #1e1e1e;
}

.hierarchy-item {
  padding: 4px 8px;
  cursor: pointer;
  border-radius: 3px;
  margin: 1px 0;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #cccccc;
  transition: all 0.2s ease;
}

.hierarchy-item:hover {
  background-color: #37373d;
  color: #ffffff;
}

.hierarchy-item.selected {
  background-color: #007acc;
  color: #ffffff;
}

.hierarchy-item.expanded > .hierarchy-icon {
  transform: rotate(90deg);
}

.hierarchy-icon {
  width: 12px;
  height: 12px;
  transition: transform 0.2s ease;
}

/* 属性面板样式 */
.properties-panel {
  background-color: #1e1e1e;
}

.property-group {
  margin-bottom: 16px;
}

.property-group-title {
  font-size: 13px;
  font-weight: 600;
  color: #cccccc;
  margin-bottom: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #3c3c3c;
}

.property-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.property-label {
  font-size: 12px;
  color: #cccccc;
  min-width: 80px;
  flex-shrink: 0;
}

.property-input {
  background-color: #3c3c3c;
  border: 1px solid #5a5a5a;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  flex: 1;
  transition: border-color 0.2s ease;
}

.property-input:focus {
  outline: none;
  border-color: #007acc;
}

/* 资产面板样式 */
.assets-panel {
  background-color: #1e1e1e;
}

.assets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
  padding: 8px;
}

.asset-item {
  background-color: #2d2d30;
  border: 1px solid #3c3c3c;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s ease;
}

.asset-item:hover {
  background-color: #37373d;
  border-color: #007acc;
}

.asset-item.selected {
  background-color: #007acc;
  border-color: #007acc;
}

.asset-thumbnail {
  width: 48px;
  height: 48px;
  background-color: #3c3c3c;
  border-radius: 3px;
  margin: 0 auto 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #cccccc;
}

.asset-name {
  font-size: 11px;
  color: #cccccc;
  word-break: break-all;
  line-height: 1.2;
}

/* 控制台面板样式 */
.console-panel {
  background-color: #1e1e1e;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.console-content {
  height: 100%;
  overflow-y: auto;
  padding: 8px;
}

.console-message {
  padding: 2px 0;
  font-size: 12px;
  line-height: 1.4;
  border-bottom: 1px solid #2d2d30;
}

.console-message.info {
  color: #cccccc;
}

.console-message.warn {
  color: #ffcc02;
}

.console-message.error {
  color: #f48771;
}

.console-message.debug {
  color: #75beff;
}

/* 状态栏样式 */
.status-bar {
  background-color: #007acc;
  color: #ffffff;
  padding: 4px 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 24px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: #1e1e1e;
}

::-webkit-scrollbar-thumb {
  background-color: #3c3c3c;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #5a5a5a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-container {
    font-size: 14px;
  }
  
  .toolbar-button {
    padding: 8px 10px;
    font-size: 14px;
  }
  
  .property-label {
    min-width: 100px;
    font-size: 14px;
  }
  
  .property-input {
    padding: 6px 10px;
    font-size: 14px;
  }
}
